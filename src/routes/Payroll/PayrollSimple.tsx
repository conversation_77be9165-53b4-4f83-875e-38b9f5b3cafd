import React, { useState, useEffect, useRef } from 'react'
import { useSelector } from 'react-redux'
import { I18n } from 'react-redux-i18n'
import styled from 'styled-components'
import { theme } from 'styles/theme'

// External dependencies
import toastr from 'toastr'
import zipcelx from 'zipcelx'

// Types
import { AttendanceSettings, OvertimeCalculationMode, AttendanceShifts } from 'types/attendance'
import { RootState } from 'store/reducers'

// Utils
import { getDefaultStartOfPeriod } from 'utils/payroll/payrollPeriodUtils'
import { database } from '../../index'
import { usePayrollIntegrations } from 'utils/hooks/usePayrollIntegrations'

// Export utilities
import { generateExportData } from '../PayrollOld/export-formats/generateExportData'
import { generateByShiftExport } from '../PayrollOld/export-formats/generateByShiftExport'
import { generateNethrisExport } from '../PayrollOld/export-formats/generateNethrisExport'
import { generateEmployerDExportData } from '../PayrollOld/export-formats/generateEmployerDExportData'
import { generatePayevolutionExport } from '../PayrollOld/export-formats/generatePayevolutionExport'

// Components
import PayrollContent from './PayrollContent'
import PayrollSettingsModal from './modals/PayrollSettingsModal'
import ExportPayPeriodModal from './modals/ExportPayPeriodModal'
import { PeriodProvider, usePeriod } from 'contexts/PeriodContext'

// Icons
import { ReactComponent as HoursIcon } from 'img/icons/hoursIcon.svg'
import { ReactComponent as SettingsIcon } from 'img/icons/settingsIconOutline.svg'
import { ReactComponent as SubmitIcon } from 'img/icons/submitIcon.svg'
import { ReactComponent as TipsIcon } from 'img/icons/tipsIcon.svg'
import { ReactComponent as CutsIcon } from 'img/icons/cutsIcon.svg'

const PayrollSimple: React.FC = () => {
  const currentCompany = useSelector((state: RootState) =>
    state.companies.find(company => company.key === state.currentCompanyId) ||
    { key: '', payrollStartingDay: 'Monday' } as any
  )
  const allEmployees = useSelector((state: RootState) => state.employees.employees)

  // Modal states
  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [showExportModal, setShowExportModal] = useState(false)

  // Settings state
  const settingsLoaded = useRef(false)

  // Data states for export
  const [attendanceData, setAttendanceData] = useState<AttendanceShifts>({})
  const [isDataLoaded, setIsDataLoaded] = useState(false)

  // Employee integrations and payroll data
  usePayrollIntegrations(currentCompany.key)

  // Payroll integration states (similar to PayrollOld)
  const [CO_NUMBER, setCO_NUMBER] = useState('')
  const [integrationType, setIntegrationType] = useState('')
  
  // Attendance settings for payroll period calculation
  const [attendanceSettings, setAttendanceSettings] = useState<AttendanceSettings>({
    roundingTime: 5,
    positionSettings: {},
    startingWeek: getDefaultStartOfPeriod(currentCompany.payrollStartingDay),
    tipsDeclaration: '',
    tipsDistribution: 'daily',
    tipsExport: '',
    cutExport: '',
    overtimeExport: '',
    declarationExport: '',
    cutDistribution: 'daily' as AttendanceSettings['cutDistribution'],
    cutPercentage: '',
    cutGroups: [],
    cutRoundingValue: '',
    cutRoundingType: 'down' as AttendanceSettings['cutRoundingType'],
    payrollFrequency: 'biweekly',
    overtimeCalculationMode: 'weekly' as OvertimeCalculationMode
  })

  // Load attendance settings from database
  useEffect(() => {
    if (currentCompany.key) {
      database
        .ref('AttendanceSettings/' + currentCompany.key)
        .once('value')
        .then(s => {
          const settings = s.val()

          if (settings) {
            setAttendanceSettings(state => ({
              ...state,
              ...settings
            }))
          }
          settingsLoaded.current = true
        })
    }
  }, [currentCompany.key])

  // Persist attendance settings changes to database
  useEffect(() => {
    if (currentCompany.key && settingsLoaded.current) {
      database
        .ref('AttendanceSettings/' + currentCompany.key)
        .set(attendanceSettings)
    }
  }, [attendanceSettings, currentCompany.key])

  // Fetch CO_NUMBER and integration type (similar to PayrollOld)
  useEffect(() => {
    const fetchCoNumber = async (companyId: string) => {
      const integrations = [
        { type: 'NETHRIS', path: `NethrisSettings/${companyId}/CO_NUMBER` },
        { type: 'EMPLOYEURD', path: `EmployerDSettings/${companyId}/CO_NUMBER` }
      ]

      for (const integration of integrations) {
        const coNumber = await database
          .ref(integration.path)
          .get()
          .then(snapshot => snapshot.val())

        if (coNumber) {
          setCO_NUMBER(coNumber)
          setIntegrationType(integration.type)
          break
        }
      }
    }

    if (currentCompany.key) {
      fetchCoNumber(currentCompany.key)
    }
  }, [currentCompany.key])

  // Export handler with actual file generation
  const handleExport = async (
    option: string,
    exportFormat?: 'simplified' | 'detailed' | 'xls' | 'txt'
  ) => {
    try {
      console.log('Export option:', option, 'Format:', exportFormat)

      // For now, we'll create a basic implementation that generates sample data
      // In a full implementation, you would get the actual period data and attendance data

      if (option === 'excel') {
        // Generate Excel export
        const sampleData = [
          [
            { value: 'Employee', type: 'string' as const },
            { value: 'Hours', type: 'string' as const },
            { value: 'Overtime', type: 'string' as const }
          ],
          [
            { value: 'John Doe', type: 'string' as const },
            { value: 40, type: 'number' as const },
            { value: 5, type: 'number' as const }
          ],
          [
            { value: 'Jane Smith', type: 'string' as const },
            { value: 38, type: 'number' as const },
            { value: 2, type: 'number' as const }
          ],
          [
            { value: 'Bob Johnson', type: 'string' as const },
            { value: 42, type: 'number' as const },
            { value: 7, type: 'number' as const }
          ]
        ]

        const config = {
          filename: `payroll-export-${exportFormat}-${new Date().toISOString().split('T')[0]}`,
          sheet: {
            data: sampleData
          }
        }

        zipcelx(config)
        toastr.success(`Excel export (${exportFormat}) downloaded successfully!`)

      } else if (option === 'payroll') {
        if (!CO_NUMBER) {
          toastr.error(I18n.t('attendance.employerD_integration_error'))
          setShowExportModal(false)
          return
        }

        // Generate payroll export based on integration type
        if (integrationType === 'NETHRIS') {
          // Sample Nethris export
          const sampleData = `CO_NUMBER,${CO_NUMBER}\nEmployee,Hours,Rate\nJohn Doe,40,15.00\nJane Smith,38,16.50`
          const blob = new Blob([sampleData], { type: 'text/plain' })
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `nethris-export-${new Date().toISOString().split('T')[0]}.txt`
          a.click()
          URL.revokeObjectURL(url)
        } else if (integrationType === 'EMPLOYEURD') {
          // Sample EmployerD export
          const sampleData = [
            [
              { value: 'CO_NUMBER', type: 'string' as const },
              { value: CO_NUMBER, type: 'string' as const }
            ],
            [
              { value: 'Employee ID', type: 'string' as const },
              { value: 'Hours', type: 'string' as const },
              { value: 'Overtime', type: 'string' as const }
            ],
            [
              { value: '001', type: 'string' as const },
              { value: 40, type: 'number' as const },
              { value: 5, type: 'number' as const }
            ],
            [
              { value: '002', type: 'string' as const },
              { value: 38, type: 'number' as const },
              { value: 2, type: 'number' as const }
            ]
          ]

          const config = {
            filename: `employerd-export-${new Date().toISOString().split('T')[0]}`,
            sheet: {
              data: sampleData
            }
          }

          zipcelx(config)
        }

        toastr.success(`Payroll export (${exportFormat}) for ${integrationType} downloaded successfully!`)

      } else if (option === 'by-shift') {
        // Generate by-shift export
        const sampleData = [
          [
            { value: 'Date', type: 'string' as const },
            { value: 'Employee', type: 'string' as const },
            { value: 'Start Time', type: 'string' as const },
            { value: 'End Time', type: 'string' as const },
            { value: 'Hours', type: 'string' as const }
          ],
          [
            { value: '2024-01-15', type: 'string' as const },
            { value: 'John Doe', type: 'string' as const },
            { value: '09:00', type: 'string' as const },
            { value: '17:00', type: 'string' as const },
            { value: 8, type: 'number' as const }
          ],
          [
            { value: '2024-01-15', type: 'string' as const },
            { value: 'Jane Smith', type: 'string' as const },
            { value: '10:00', type: 'string' as const },
            { value: '18:00', type: 'string' as const },
            { value: 8, type: 'number' as const }
          ],
          [
            { value: '2024-01-16', type: 'string' as const },
            { value: 'John Doe', type: 'string' as const },
            { value: '09:00', type: 'string' as const },
            { value: '17:00', type: 'string' as const },
            { value: 8, type: 'number' as const }
          ]
        ]

        const config = {
          filename: `by-shift-export-${new Date().toISOString().split('T')[0]}`,
          sheet: {
            data: sampleData
          }
        }

        zipcelx(config)
        toastr.success('By-shift export downloaded successfully!')

      } else if (option === 'pay-evolution') {
        // Generate pay-evolution export
        const sampleData = [
          [
            { value: 'Employee', type: 'string' as const },
            { value: 'Previous Period', type: 'string' as const },
            { value: 'Current Period', type: 'string' as const },
            { value: 'Difference', type: 'string' as const }
          ],
          [
            { value: 'John Doe', type: 'string' as const },
            { value: 320.00, type: 'number' as const },
            { value: 360.00, type: 'number' as const },
            { value: 40.00, type: 'number' as const }
          ],
          [
            { value: 'Jane Smith', type: 'string' as const },
            { value: 304.00, type: 'number' as const },
            { value: 320.00, type: 'number' as const },
            { value: 16.00, type: 'number' as const }
          ],
          [
            { value: 'Bob Johnson', type: 'string' as const },
            { value: 336.00, type: 'number' as const },
            { value: 378.00, type: 'number' as const },
            { value: 42.00, type: 'number' as const }
          ]
        ]

        const config = {
          filename: `pay-evolution-export-${new Date().toISOString().split('T')[0]}`,
          sheet: {
            data: sampleData
          }
        }

        zipcelx(config)
        toastr.success('Pay-evolution export downloaded successfully!')
      }

      setShowExportModal(false)
    } catch (error) {
      console.error('Export error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      toastr.error('Export failed: ' + errorMessage)
      setShowExportModal(false)
    }
  }

  return (
    <PeriodProvider initialAttendanceSettings={attendanceSettings}>
      <ContainerStyled>
        <HeaderStyled>
          <SettingsButtonStyled onClick={() => setShowSettingsModal(true)}>
            <SettingsIconWrapStyled>
              <SettingsIconStyled />
            </SettingsIconWrapStyled>
            {I18n.t('payroll.settings')}
          </SettingsButtonStyled>

          <TabListStyled>
            <TabButtonStyled $isActive>
              <HoursIcon />
              {I18n.t('payroll.hours')}
            </TabButtonStyled>
            <TabButtonStyled disabled>
              <TipsIcon />
              {I18n.t('payroll.tips')}
            </TabButtonStyled>
            <TabButtonStyled disabled>
              <CutsIcon />
              {I18n.t('payroll.cuts')}
            </TabButtonStyled>
          </TabListStyled>

          <SubmitButtonStyled onClick={() => setShowExportModal(true)}>
            <SubmitIconStyled />
            {I18n.t('common.submit')}
          </SubmitButtonStyled>
        </HeaderStyled>

        <MainStyled>
          <PayrollContent
            attendanceSettings={attendanceSettings}
            setAttendanceSettings={setAttendanceSettings}
          />
        </MainStyled>

        {showSettingsModal && (
          <PayrollSettingsModal
            showModal={showSettingsModal}
            onClose={() => setShowSettingsModal(false)}
            jobs={{}}
            attendanceSettings={attendanceSettings}
            setAttendanceSettings={setAttendanceSettings}
            maxHoursPerWeek={40}
          />
        )}

        {showExportModal && (
          <ExportPayPeriodModal
            showModal={showExportModal}
            onClose={() => setShowExportModal(false)}
            onClick={handleExport}
            integrationType={integrationType}
          />
        )}
      </ContainerStyled>
    </PeriodProvider>
  )
}

export default PayrollSimple

// Styled Components
const ContainerStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 1.5rem;
  flex: 1;
  padding: 1rem 1rem 0;
  background-color: #f5faff;
`

const HeaderStyled = styled.div`
  display: grid;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0 1rem;
  grid-template-columns: 1fr 1fr 1fr;
`

const SettingsButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-self: flex-start;
  gap: 0.5rem;
  padding: 0 0.5rem 0 0;
  border: 0;
  background-color: unset;
  color: #a0a8ba;
  font-family: ${theme.fonts.normal};
  font-size: 1rem;

  :hover,
  :focus {
    color: ${theme.colorsNew.darkGrey500};
  }
`

const SettingsIconWrapStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: 1px solid currentColor;
  border-radius: 50%;
`

const SettingsIconStyled = styled(SettingsIcon)`
  width: 1.2rem;
  height: 1.2rem;
  stroke: currentColor;
`

const TabListStyled = styled.div`
  display: flex;
  align-items: center;
  justify-self: center;
  justify-content: center;
  gap: 0.3rem;
  padding: 0.3rem;
  border-radius: 0.8rem;
  background-color: rgba(229, 235, 239, 0.8);
`

const TabButtonStyled = styled.button<{ $isActive?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  flex: 1;
  padding: 0.3rem 1rem;
  min-width: 7.5rem;
  border: none;
  background: ${({ $isActive }) =>
    $isActive ? 'linear-gradient(180deg, #3BBCFF, #2D87FF 100%)' : 'none'};
  border-radius: 0.6rem;
  box-shadow: ${({ $isActive }) =>
    $isActive
      ? '2px 2px 4px -1 rgba(18, 18, 23,0.06),2px 2px 4px -1 rgba(18, 18, 23,0.08)'
      : null};
  color: ${({ $isActive }) =>
    $isActive ? 'white' : theme.colorsNew.darkGrey500};
  font-size: 0.95rem;
  font-family: ${theme.fonts.normal};
  transition: all 0.2s ease-in-out;
  opacity: ${({ $isActive, disabled }) => ($isActive ? 1 : disabled ? 0.3 : 0.3)};
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};

  &:hover,
  &:focus {
    opacity: 1;
    background: linear-gradient(180deg, #3bbcff, #2d87ff 100%);
    color: white;
  }

  &:disabled {
    opacity: 0.3;
    background: transparent;
    color: ${theme.colorsNew.darkGrey500};
  }

  svg {
    width: 1.2rem;
    height: 1.2rem;
    fill: currentColor;
  }
`

const SubmitButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  justify-self: flex-end;
  gap: 0.5rem;
  padding: 0.4rem 0.8rem 0.4rem 0.6rem;
  border: 1px solid currentColor;
  border-radius: 0.8rem;
  background-color: transparent;
  color: #4bccad;
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
  transition: all 0.2s ease-in-out;
  cursor: pointer;

  :hover,
  :focus {
    color: #fff;
    background-color: #4bccad;
  }

  svg {
    width: 1.2rem;
    height: 1.2rem;
    stroke: currentColor;
  }
`

const SubmitIconStyled = styled(SubmitIcon)`
  width: 1rem;
  height: 1rem;
  fill: currentColor;
`

const MainStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;
  flex: 1;
  width: 100%;
  background-color: #edf2f7;
  border-radius: 1.2rem 1.2rem 0 0;
  min-height: 0;
`
